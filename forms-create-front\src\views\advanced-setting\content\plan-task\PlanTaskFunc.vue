<template>
  <div class="mould_pop select_task_pop">
    <div class="pop_top">选择任务模块</div>

    <div class="pop_cont">
      <div class="sltp_cont">
        <div v-for="ele in executePlanDataList" :key="ele.type" class="sltp_per mb20">
          <h6 class="sltp_per_title">{{ ele.title }}</h6>
          <p class="sltp_per_tip">说明 {{ ele.remark }}</p>
          <span class="sltp_per_btn" @click.stop="doSelOneExecutePlan(ele)">选择</span>
        </div>
      </div>
    </div>
    <div class="pop_btm">
      <span class="pop_cal" @click.stop="emit('colse')">取消</span>
    </div>
    <!--弹窗-定时更新时间间隔模块-->
    <plan-task-func-timing
      v-if="currPlanTaskConfig.currPlanTaskModelType.type === 'planTaskOfTiming' && executePlanCurrModelShow"
      ref="planTaskFuncTimingRef"
      @get-model-config="$getModelConfig"
    ></plan-task-func-timing>

    <!--弹窗-日期超过指定期限修改字段值-->
    <plan-task-func-field-by-date
      v-if="currPlanTaskConfig.currPlanTaskModelType.type === 'planTaskOfDatePass' && executePlanCurrModelShow"
      ref="planTaskFuncFieldByDateRef"
      @get-model-config="$getModelConfig"
    ></plan-task-func-field-by-date>

    <!--弹窗-数据更新-->
    <plan-task-data-update
      v-if="currPlanTaskConfig.currPlanTaskModelType.type === 'planTaskOfDataUpdate' && executePlanCurrModelShow"
      ref="planTaskDataUpdateRef"
      @get-model-config="$getModelConfig"
    ></plan-task-data-update>

    <!-- 弹窗-修改字段值 -->

    <plan-task-change-data
      v-if="currPlanTaskConfig.currPlanTaskModelType.type === 'planTaskOfDataModify' && executePlanCurrModelShow"
      ref="planTaskChangeData"
      @get-model-config="$getModelConfig"
    ></plan-task-change-data>
  </div>
</template>

<script lang="ts" setup>
import { Ref } from 'vue'
import PlanTaskFuncTiming from './PlanTaskFuncTiming.vue'
import PlanTaskFuncFieldByDate from './PlanTaskFuncFieldByDate.vue'
import PlanTaskDataUpdate from './PlanTaskDataUpdate.vue'
import PlanTaskChangeData from './PlanTaskChangeData.vue'
import { CurrPlanTaskConfigI, currPlanTaskModelConfigI, executePlanDataListD, ExecutePlanDataObjI } from './hooks/PlanTaskTypes'

// const props = defineProps({
//   currPlanTaskConfig: {
//     type: Object,
//     default() {
//       return {}
//     }
//   }
// })
// const prop = toRefs(props)

const emit = defineEmits(['colse', 'do-sel-one-execute-plan', 'check-disable-add-status'])

/* 接受父组件PlanTaskAdd.vue传递的参数 */
const currPlanTaskConfig: Ref<CurrPlanTaskConfigI> = inject('currPlanTaskConfig') as Ref<CurrPlanTaskConfigI>

/* 所有的任务模块数据对象 */
const executePlanDataList = ref(executePlanDataListD)

/* 控制具体的任务模块弹窗显示隐藏 */
const executePlanCurrModelShow = ref(false)

/**
 * @description: 打开关闭具体的任务模块弹窗
 * @return {*}
 */
function $executePlanAreaToggle(): void {
  executePlanCurrModelShow.value = !executePlanCurrModelShow.value
}

const planTaskFuncTimingRef: any = ref(null)
const planTaskFuncFieldByDateRef: any = ref(null)
const planTaskDataUpdateRef: any = ref(null)
const planTaskChangeData: any = ref(null)
/**
 * @description: 选择具体的模块后, 加载对于组件的配置
 * @param {*} type 模块名称
 * @return {*}
 */
function loadModelConfig(type: string): void {
  switch (type) {
    case 'planTaskOfTiming':
      planTaskFuncTimingRef.value!.$initConfig(currPlanTaskConfig.value.currPlanTaskModelConfig)
      break
    case 'planTaskOfDatePass':
      planTaskFuncFieldByDateRef.value!.$initConfig(currPlanTaskConfig.value.currPlanTaskModelConfig)
      break
    case 'planTaskOfDataUpdate':
      planTaskDataUpdateRef.value!.$initConfig(currPlanTaskConfig.value.currPlanTaskModelConfig)
      break
    case 'planTaskOfDataModify':
      planTaskChangeData.value!.$initConfig(currPlanTaskConfig.value.currPlanTaskModelConfig)
      break
    default:
      break
  }
}

/**
 * @description: 选择任务模块
 * @param {*} ele 选择的任务模块配置对象
 * @return {*}
 */
const doSelOneExecutePlan = (ele: ExecutePlanDataObjI): void => {
  emit('do-sel-one-execute-plan', ele)
  $executePlanAreaToggle()
  nextTick(() => {
    loadModelConfig(ele.type as string)
  })
}

/**
 * @description: 子组件回传配置好的执行任务模块配置内容
 * @return {*}
 */
function $getModelConfig(config: currPlanTaskModelConfigI): void {
  if (Object.keys(config).length > 0) {
    currPlanTaskConfig.value.currPlanTaskModelType.saved = true
    currPlanTaskConfig.value.currPlanTaskModelConfig = config
  }
  $executePlanAreaToggle()
  emit('colse')
}

/**
 * @description: 提供父组件调用, 编辑某个任务模块时调用
 * @return {*}
 */
function $resetExecutePlan(): void {
  $executePlanAreaToggle()
  nextTick(() => {
    loadModelConfig(currPlanTaskConfig.value.currPlanTaskModelType.type as string)
  })
}

defineExpose({
  $resetExecutePlan
})
</script>

<style></style>
