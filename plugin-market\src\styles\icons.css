@font-face {
  font-family: 'icomoon';
  src: url('../assets/fonts/icomoon.eot?k4s8h');
  src: url('../assets/fonts/icomoon.eot?k4s8h#iefix') format('embedded-opentype'),
    url('../assets/fonts/icomoon.ttf?k4s8h') format('truetype'),
    url('../assets/fonts/icomoon.woff?k4s8h') format('woff'),
    url('../assets/fonts/icomoon.svg?k4s8h#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@-moz-document url-prefix() {
  [class^="icon-"], [class*=" icon-"] {
    line-height: 1.1;
  }
}

/* 基础图标样式 */
[class^='icon-'] {
  font-size: 18px;
}

/* 字段图标定义 */
.icon-field-edit-input:before {
  content: "\e924";
  color: #006ce2;
}

.icon-filed-number-input:before {
  content: "\e922";
  color: #006ce2;
}

.icon-field-select-box:before {
  content: "\e92a";
  color: #006ce2;
}

.icon-field-select-multi-box:before {
  content: "\e98d";
  color: #006ce2;
}

.icon-field-date-input:before {
  content: "\e966";
  color: #006ce2;
}

/* 其他常用图标 */
.icon-set:before {
  content: "\e938";
  color: #4d4d4d;
}
