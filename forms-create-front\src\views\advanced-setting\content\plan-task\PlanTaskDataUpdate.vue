<template>
  <div class="mould_pop update_time_pop">
    <div class="pop_top">数据更新【功能模块配置】</div>
    <div class="pop_cont">
      <div class="utp_cont_table">
        <div class="data_update_title">
          <span class="title_left">更新字段</span>
          <span class="title_right">支持更新满足条件数据中所选字段的默认值</span>
        </div>
        <div class="utp_cont_table-wrapper">
          <el-scrollbar>
            <div class="utp_cont_table--content">
              <match-table ref="matchTableRef" thead-class="match-table__thead--sticky" :tbody-data="currFields" match-prop="field.label">
                <template #th>
                  <th width="68px"><!--<input type="checkbox" :checked="isAllChecked" @click="selectAllChecked" />--></th>
                  <th class="td--center" width="82px">序号</th>
                  <th>字段名称</th>
                  <th width="120px">字段类型</th>
                </template>
                <template #td="{ data: compt, index }">
                  <td>
                    <input
                      id="noCheck"
                      type="checkbox"
                      :checked="compt.checked"
                      :disabled="disabledFieldMap[compt.field.cid]"
                      @click="compt.checked = !compt.checked"
                    />
                  </td>
                  <td class="td--center">{{ index + 1 }}</td>
                  <td :title="compt.field.label" class="field-name" :class="{ disabled: disabledFieldMap[compt.field.cid] }">
                    {{ compt.field.label }}
                  </td>
                  <td :style="{ 'text-align': 'right' }">
                    <span class="option-compt">{{ getComptType(compt.field.compt, compt.field.id) }}</span>
                  </td>
                </template>
              </match-table>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
    <div class="pop_btm">
      <span class="pop_cal" @click.stop="doCancel">取消</span>
      <span class="pop_sure" :class="{ noClick: isEmptyChecked }" @click.stop="doSaveConfig">保存配置</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useFormStore } from '@/store'
import { currPlanTaskModelConfigI, DataUpdateListI } from './hooks/PlanTaskTypes'
import { BaseFieldConfig, DatetimeRange, FieldConfig, LeaveKit, Subform } from '@/components/Application/Form/types'
import { getComptType } from '@/utils/utils'

interface FrontField {
  cid: number
  pcid: number
  label: string
  compt: string
}

interface DataUpdateField {
  field: FrontField
  checked: boolean
}

const emit = defineEmits(['get-model-config'])

// 表单字段
const { formFields } = useFormStore()

// 当前字段
const currFields = reactive([] as DataUpdateField[])

const toFields = (comptConfig: FieldConfig, parentField: FrontField | null) => {
  const noUseCompt = ['captiontext', 'relateddata', 'relatedcompt']
  if (noUseCompt.includes(comptConfig.compt)) {
    return null
  }

  let label = ''
  if (parentField) label = `${parentField.label}.`
  switch (comptConfig.compt) {
    case 'matrixradio':
    case 'matrixcheckbox':
    case 'detailcombox':
      label = (comptConfig as any).label
      break
    case 'datetimerange':
      label += `${(comptConfig as DatetimeRange).fields[0].label}-${(comptConfig as DatetimeRange).fields[1].label}`
      break
    case 'leavekit': {
      const newCompt = comptConfig as LeaveKit
      label += `${newCompt.fields[0].label}-${newCompt.fields[1].label}-${newCompt.fields[2].label}`
      break
    }
    default:
      label += (comptConfig as BaseFieldConfig).fields[0].label
      break
  }
  return {
    cid: comptConfig.id,
    pcid: (parentField && parentField.cid) || 0,
    label,
    compt: comptConfig.compt
  }
}

/**
 * 将表单结构转换成前端展示所需的信息结构
 */
const fieldsList = () => {
  const fields = []
  for (let i = 0; i < formFields.length; i++) {
    const comptConfig = formFields[i]
    const field = toFields(comptConfig, null)
    if (!field) continue
    if (field.compt === 'detailcombox') {
      // 添加子表单整体
      fields.push(field)
      for (let j = 0; j < (comptConfig as Subform).compts.length; j++) {
        const subField = toFields((comptConfig as Subform).compts[j], field)
        if (subField) fields.push(subField)
      }
      continue
    }
    fields.push(field)
  }
  return fields
}

// 初始化字段信息及其结构
const initFields = (configFields: DataUpdateListI[]) => {
  fieldsList().forEach((field) => {
    const isChecked = configFields.some((data: DataUpdateListI) => field.cid === data.cid && field.pcid === data.pcid)
    currFields.push({ field, checked: isChecked })
  })
}

// 是否全部可编辑
const isAllChecked = computed(() => {
  return currFields.every((per) => per.checked)
})

// 全选/全不选
const selectAllChecked = () => {
  const checked = isAllChecked.value
  currFields.map((item: DataUpdateField) => {
    item.checked = !checked
    return true
  })
}
const selectedSubFormFields = computed(() => {
  return currFields.filter((field) => (field.field.compt === 'detailcombox' || field.field.pcid > 0) && field.checked) || []
})
const disabledFieldMap = computed(() => {
  // 子表单整体和子表单内字段互斥
  const map = {}
  currFields.forEach((field) => {
    if (field.field.pcid > 0 && selectedSubFormFields.value.filter((selected) => selected.field.cid === field.field.pcid).length > 0) {
      map[field.field.cid] = true
    } else if (
      field.field.compt === 'detailcombox' &&
      selectedSubFormFields.value.filter((selected) => selected.field.pcid === field.field.cid).length > 0
    ) {
      map[field.field.cid] = true
    }
  })
  return map
})

// 没有选择的字段
const isEmptyChecked = computed(() => currFields.every((per) => !per.checked))

/**
 * @description: 取消配置 关闭弹窗
 * @return {*}
 */
function doCancel(): void {
  emit('get-model-config', {})
}

/**
 * @description: 保存弹窗内配置
 * @return {*}
 */
function doSaveConfig(): void {
  const data = [] as DataUpdateListI[]
  currFields.forEach((item: DataUpdateField) => {
    if (item.checked) {
      const compt: DataUpdateListI = {
        cid: item.field.cid,
        compt: item.field.compt,
        pcid: item.field.pcid
      }
      data.push(compt)
    }
  })
  emit('get-model-config', { dataUpdateField: data })
}

/**
 * @description: 初始化弹窗配置 提供父组件调用
 * @return {*}
 */
function $initConfig(config: currPlanTaskModelConfigI): void {
  initFields(config.dataUpdateField || ([] as DataUpdateListI[]))
}

defineExpose({ $initConfig })
</script>

<style lang="scss" scoped>
.field-name {
  max-width: 150px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.utp_cont_table--content {
  table {
    td.disabled {
      color: #898989;
    }
  }
}
</style>
