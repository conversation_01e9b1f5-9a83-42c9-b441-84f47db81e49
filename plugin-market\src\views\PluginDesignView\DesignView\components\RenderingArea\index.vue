<template>
  <div class="rendering-area widget-apply">
    <draggable
      :key="mainFields.length"
      v-model="mainFields"
      class="plugin-field-list form-widget-list"
      tag="ul"
      :group="{ name: 'shared' }"
      :swap-threshold="0.2"
      :scroll-sensitivity="10"
      data-empty-tip="请从左侧拖拽或点击来添加字段"
      @add="addFieldComp"
    >
      <li
        v-for="comp in mainFieldCompts"
        :id="`compt${comp.id}`"
        :key="comp.id"
        class="widget-item-flex"
        :class="{ active: selectedField && selectedField.id === comp.id }"
        @dragstart="flagDraggingFieldType(comp.value)"
        @click.stop="handleFieldClick(comp.value)"
      >
        <component :is="comp.compt" :compt="comp.value" :parent-fields="mainFields"></component>
      </li>
    </draggable>
  </div>
</template>
<script lang="ts" setup>
import { computed, toRefs, ref } from 'vue'
import { VueDraggableNext as draggable } from 'vue-draggable-next'
import { usePluginDesignStore } from '@/stores'
import { type ComponentType, type FieldDataType } from '../../types'
import useAddField from '../../hooks/useAddField'
import { useDesignComponent } from '@/hooks/useDesignComponent'

const pluginDesignStore = usePluginDesignStore()
const { mainFields, selectedField } = toRefs(pluginDesignStore)
const { centerComponent } = useDesignComponent()

const mainFieldCompts = computed(() => {
  return mainFields.value.map((field: FieldDataType) => {
    console.log(field, centerComponent.get(field.compt))

    return {
      id: field.id,
      compt: centerComponent.get(field.compt),
      value: field
    }
  })
})

console.log(mainFieldCompts.value, mainFields)

const addFieldComp = () => {
  const comptType = pluginDesignStore.draggingFieldInfo.type

  if (comptType) {
    useAddField(comptType)
  }
}

function flagDraggingFieldType(field: FieldDataType) {
  pluginDesignStore.setDraggingFieldType(field.compt as ComponentType)
}

// 点击字段时设置当前选中的字段
function handleFieldClick(field: FieldDataType) {
  pluginDesignStore.setSelectedField(field)
}
</script>

<style lang="scss">
.widget-item-flex {
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #eaecf2;
  border-radius: 4px;
  cursor: pointer;

  &.active {
    /* 不改变边框颜色，保持原来的样式 */
    /* border-color: #0057ff; */
    /* background-color: #e5eeff; */
  }
}
</style>
