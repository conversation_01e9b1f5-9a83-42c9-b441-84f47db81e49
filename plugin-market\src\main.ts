import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { directives } from '@cx-office/shared'

import App from './App.vue'
import router from './router'

import '@unocss/reset/tailwind.css'
import 'uno.css'
import '@cx-office/shared/styles/common/index.scss'
import './styles/icons.css'

const app = createApp(App)

app.use(directives)
app.use(createPinia())
app.use(router)

app.mount('#app')
