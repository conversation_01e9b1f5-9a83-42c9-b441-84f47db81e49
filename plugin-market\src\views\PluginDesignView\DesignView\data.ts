import { type MaterialFieldType } from './types'

// 插件设计页中的左侧菜单
export const materialFieldsData: MaterialFieldType[] = [
  {
    title: '基础字段',
    type: 'base',
    list: [
      {
        compt: 'editinput',
        title: '单行输入',
        class: 'icon-field-edit-input'
      },
      {
        compt: 'numberinput',
        title: '数字输入',
        class: 'icon-filed-number-input'
      },
      {
        compt: 'selectbox',
        title: '下拉框',
        class: 'icon-field-select-box'
      },
      {
        compt: 'selectmultibox',
        title: '下拉复选',
        class: 'icon-field-select-multi-box'
      },
      {
        compt: 'dateinput',
        title: '日期',
        class: 'icon-field-date-input'
      }
    ]
  },
  {
    title: '功能字段',
    type: 'function',
    list: [
      {
        compt: 'fieldselect',
        title: '字段选择',
        class: 'icon-field-select'
      }
    ]
  }
]
