<template>
  <div class="field-component" :class="{ selected: isSelected }" @click="handleFieldClick">
    <!-- 字段标题 -->
    <div class="field-title">
      <span v-if="compt.compt === 'editinput'">单行输入</span>
      <span v-else-if="compt.compt === 'numberinput'">数字输入</span>
      <span v-else-if="compt.compt === 'selectbox'">下拉框</span>
      <span v-else-if="compt.compt === 'selectmultibox'">下拉复选</span>
      <span v-else-if="compt.compt === 'dateinput'">日期</span>
      <span v-else-if="compt.compt === 'fieldselect'"></span>
      <span v-else>未知组件</span>
    </div>

    <!-- 输入框 -->
    <div class="field-input">
      <!-- 下拉框 -->
      <select v-if="compt.compt === 'selectbox'" v-model="selectValue" class="select-field">
        <option value="" disabled>{{ getPlaceholder() }}</option>
        <option v-for="option in selectOptions" :key="option.value" :value="option.value">
          {{ option.label }}
        </option>
      </select>
      <!-- 多选框 -->
      <div v-else-if="compt.compt === 'selectmultibox'" class="multi-select-wrapper">
        <div class="multi-select-field" @click="toggleDropdown">
          <span class="multi-select-display">{{ getSelectedOptionsText() }}</span>
          <span class="dropdown-arrow" :class="{ open: isDropdownOpen }">▼</span>
        </div>
        <div v-if="isDropdownOpen" class="multi-select-dropdown">
          <label v-for="option in multiSelectOptions" :key="option.value" class="dropdown-checkbox-item">
            <input v-model="multiSelectValues" :value="option.value" type="checkbox" class="checkbox-input" />
            <span class="checkbox-custom"></span>
            <span class="checkbox-label">{{ option.label }}</span>
          </label>
        </div>
      </div>
      <!-- 日期输入框 -->
      <div v-else-if="compt.compt === 'dateinput'" class="date-input-wrapper">
        <input v-model="dateValue" type="datetime-local" class="date-input" :placeholder="getPlaceholder()" />
        <span class="date-icon">📅</span>
      </div>
      <!-- 字段选择 -->
      <div v-else-if="compt.compt === 'fieldselect'" class="field-select-wrapper">
        <div class="field-select-header">
          <span class="field-select-title">字段选择</span>
        </div>
        <div class="field-select-group">
          <select v-for="(fieldConfig, index) in dynamicFieldConfigs" :key="index" v-model="fieldSelectValues[index]" class="field-select-item">
            <option value="" disabled>{{ fieldConfig.placeholder || '请选择' }}</option>
            <option v-for="option in getFieldOptions(fieldConfig.type)" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
      </div>
      <!-- 普通输入框 -->
      <input v-else v-model="inputValue" type="text" :placeholder="getPlaceholder()" class="input-field" />
    </div>

    <!-- 右上角操作按钮 -->
    <div class="field-actions">
      <button class="action-btn copy-btn" title="复制" @click="handleCopy">
        <i class="icon-copy"></i>
      </button>
      <button class="action-btn delete-btn" title="删除" @click="handleDelete">
        <i class="icon-delete"></i>
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { type PropType, ref, computed } from 'vue'
import { type FieldDataType } from '../../../views/PluginDesignView/DesignView/types'
import { usePluginDesignStore } from '../../../stores'

const props = defineProps({
  compt: {
    type: Object as PropType<FieldDataType>,
    required: true
  },
  parentFields: {
    type: Array,
    default: () => []
  }
})

const pluginDesignStore = usePluginDesignStore()

// 判断当前字段是否被选中
const isSelected = computed(() => {
  return pluginDesignStore.selectedField?.id === props.compt.id
})

// 处理字段点击事件
const handleFieldClick = () => {
  pluginDesignStore.setSelectedField(props.compt)
}

// 输入框的值
const inputValue = ref('')

// 下拉框的值
const selectValue = ref('')

// 多选框的值
const multiSelectValues = ref([])

// 下拉框展开状态
const isDropdownOpen = ref(false)

// 日期输入的值
const dateValue = ref('')

// 字段选择的值 - 动态字段配置
const fieldSelectValues = ref<string[]>([])

// 动态字段配置（从store获取或使用默认配置）
const dynamicFieldConfigs = computed(() => {
  // 这里应该从store获取配置，暂时使用默认配置
  const defaultConfigs = [
    { name: '请输入', placeholder: '请输入学号', type: 'type1' },
    { name: '单行输入', placeholder: '请输入学号', type: 'type3' },
    { name: '下拉框', placeholder: '请输入', type: 'type4' }
  ]

  // 初始化fieldSelectValues数组
  if (fieldSelectValues.value.length !== defaultConfigs.length) {
    fieldSelectValues.value = new Array(defaultConfigs.length).fill('')
  }

  return defaultConfigs
})

// 根据字段类型获取选项
const getFieldOptions = (type: string) => {
  const optionsMap: Record<string, Array<{ value: string; label: string }>> = {
    type1: [
      { value: 'option1_1', label: '字段类型1选项1' },
      { value: 'option1_2', label: '字段类型1选项2' }
    ],
    type3: [
      { value: 'option3_1', label: '字段类型3选项1' },
      { value: 'option3_2', label: '字段类型3选项2' }
    ],
    type4: [
      { value: 'option4_1', label: '字段类型4选项1' },
      { value: 'option4_2', label: '字段类型4选项2' }
    ]
  }

  return optionsMap[type] || []
}

// 下拉框选项（从store获取）
const selectOptions = computed(() => {
  if (props.compt.compt === 'selectbox') {
    const config = pluginDesignStore.getFieldConfig(props.compt.id)
    return (
      config.options || [
        { value: 'option1', label: '选项1' },
        { value: 'option2', label: '选项2' },
        { value: 'option3', label: '选项3' }
      ]
    )
  }
  return []
})

// 多选框选项（从store获取）
const multiSelectOptions = computed(() => {
  if (props.compt.compt === 'selectmultibox') {
    const config = pluginDesignStore.getFieldConfig(props.compt.id)
    return (
      config.options || [
        { value: 'option1', label: '选项1' },
        { value: 'option2', label: '选项2' },
        { value: 'option3', label: '选项3' }
      ]
    )
  }
  return []
})

// 切换下拉框展开状态
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
}

// 获取选中选项的显示文本
const getSelectedOptionsText = () => {
  if (multiSelectValues.value.length === 0) {
    return getPlaceholder()
  }
  const selectedLabels = multiSelectOptions.value.filter((option) => multiSelectValues.value.includes(option.value)).map((option) => option.label)

  if (selectedLabels.length <= 2) {
    return selectedLabels.join(', ')
  }
  return `${selectedLabels.slice(0, 2).join(', ')} 等${selectedLabels.length}项`
}

// 旧的字段选择逻辑已移除，现在使用动态配置

// 获取占位符文本
const getPlaceholder = () => {
  switch (props.compt.compt) {
    case 'editinput':
      return '请输入'
    case 'numberinput':
      return '请输入数字'
    case 'selectbox':
      return '请选择'
    case 'selectmultibox':
      return '请选择多项'
    case 'dateinput':
      return '请选择日期'
    case 'fieldselect':
      return '请选择字段'
    default:
      return '请输入'
  }
}

// 复制字段
const handleCopy = () => {
  // 创建字段的副本
  const newField = {
    ...props.compt,
    id: pluginDesignStore.getFieldId(), // 生成新的ID
    alias: `${props.compt.alias}_copy` // 修改别名避免重复
  }

  // 添加到主字段列表
  pluginDesignStore.setMainFields(newField)
}

// 删除字段
const handleDelete = () => {
  // 从主字段列表中删除当前字段
  pluginDesignStore.removeMainField(props.compt.id)
}
</script>

<style lang="scss" scoped>
.field-component {
  position: relative;
  padding: 12px;
  border: 1px solid #eaecf2;
  border-radius: 4px;
  background: #fff;
  transition: all 0.2s ease;

  &:hover {
    .field-actions {
      opacity: 1;
    }
  }

  &.selected {
    border-color: #eaecf2; /* 保持原来的边框颜色不变 */

    .field-title {
      background: rgb(229, 238, 255);

      color: #1890ff;
      margin: 0 0 8px 0; /* 不改变外边距，保持内部结构 */
      padding: 0 0 4px 0; /* 只添加底部内边距 */
      border-bottom: 1px solid #e6f7ff; /* 只添加底部边框 */
    }
  }
}

.field-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.field-input {
  .input-field,
  .select-field {
    width: 100%;
    height: 32px;
    padding: 0 8px;

    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: #fff;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &::placeholder {
      color: #999;
    }
  }

  .select-field {
    cursor: pointer;

    option {
      padding: 4px 8px;
    }
  }

  // 多选下拉框样式
  .multi-select-wrapper {
    position: relative;
    width: 100%;
  }

  .multi-select-field {
    width: 100%;
    height: 32px;
    padding: 0 32px 0 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: border-color 0.2s ease;
    position: relative;

    &:hover {
      border-color: #1890ff;
    }
  }

  .multi-select-display {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dropdown-arrow {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: #999;
    transition: transform 0.2s ease;

    &.open {
      transform: translateY(-50%) rotate(180deg);
    }
  }

  .multi-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .dropdown-checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .checkbox-input {
    display: none;
  }

  .checkbox-custom {
    width: 16px;
    height: 16px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    position: relative;
    transition: all 0.2s ease;
    cursor: pointer;

    &::after {
      content: '';
      width: 8px;
      height: 4px;
      border: 2px solid #1890ff;
      border-top: none;
      border-right: none;
      position: absolute;
      top: 3px;
      left: 3px;
      transform: rotate(-45deg) scale(0);
      transition: transform 0.2s ease;
    }
  }

  .checkbox-input:checked + .checkbox-custom {
    border-color: #1890ff;
    background: #1890ff;

    &::after {
      border-color: #fff;
      transform: rotate(-45deg) scale(1);
    }
  }

  .checkbox-label {
    font-size: 14px;
    color: #333;
    cursor: pointer;
    user-select: none;
  }

  // 日期输入框样式
  .date-input-wrapper {
    position: relative;
    width: 100%;
  }

  .date-input {
    width: 100%;
    height: 32px;
    padding: 0 32px 0 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: #fff;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &::placeholder {
      color: #999;
    }
  }

  .date-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #999;
    pointer-events: none;
  }

  // 字段选择样式
  .field-select-wrapper {
    width: 100%;
  }

  .field-select-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 0 4px;
  }

  .field-select-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  .field-select-hint {
    font-size: 12px;
    color: #1890ff;
    cursor: default;
  }

  .field-select-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .field-select-item {
    flex: 1;
    height: 32px;
    padding: 0 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: #fff;
    outline: none;
    cursor: pointer;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:disabled {
      background-color: #f5f5f5;
      color: #999;
      cursor: not-allowed;
      border-color: #d9d9d9;
    }

    option {
      padding: 4px 8px;
    }
  }
}

.field-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.action-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }
}

.copy-btn:hover {
  background-color: #e6f7ff;
}

.delete-btn:hover {
  background-color: #fff2f0;
}
</style>

<style lang="scss" scoped>
.field-component {
  padding: 8px;
  font-size: 14px;
  color: #333;
}
</style>
