<template>
  <div class="field-component">
    <div v-if="compt.compt === 'editinput'">单行输入</div>
    <div v-else-if="compt.compt === 'numberinput'">数字输入</div>
    <div v-else-if="compt.compt === 'selectbox'">下拉框</div>
    <div v-else-if="compt.compt === 'selectmultibox'">下拉复选</div>
    <div v-else-if="compt.compt === 'dateinput'">日期</div>
    <div v-else-if="compt.compt === 'fieldselect'">字段选择</div>
    <div v-else>未知组件</div>
  </div>
</template>

<script lang="ts" setup>
import { type PropType } from 'vue'
import { type FieldDataType } from '@/views/PluginDesignView/DesignView/types'

defineProps({
  compt: {
    type: Object as PropType<FieldDataType>,
    required: true
  },
  parentFields: {
    type: Array,
    default: () => []
  }
})
</script>

<style lang="scss" scoped>
.field-component {
  padding: 8px;
  font-size: 14px;
  color: #333;
}
</style>
