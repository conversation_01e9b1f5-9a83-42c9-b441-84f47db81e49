<template>
  <div class="field-component">
    <!-- 字段标题 -->
    <div class="field-title">
      <span v-if="compt.compt === 'editinput'">单行输入</span>
      <span v-else-if="compt.compt === 'numberinput'">数字输入</span>
      <span v-else-if="compt.compt === 'selectbox'">下拉框</span>
      <span v-else-if="compt.compt === 'selectmultibox'">下拉复选</span>
      <span v-else-if="compt.compt === 'dateinput'">日期</span>
      <span v-else-if="compt.compt === 'fieldselect'">字段选择</span>
      <span v-else>未知组件</span>
    </div>

    <!-- 输入框 -->
    <div class="field-input">
      <input
        v-model="inputValue"
        type="text"
        :placeholder="getPlaceholder()"
        class="input-field"
      />
    </div>

    <!-- 右上角操作按钮 -->
    <div class="field-actions">
      <button class="action-btn copy-btn" title="复制" @click="handleCopy">
        <i class="icon-copy"></i>
      </button>
      <button class="action-btn delete-btn" title="删除" @click="handleDelete">
        <i class="icon-delete"></i>
      </button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { type PropType, ref } from 'vue'
import { type FieldDataType } from '../../../views/PluginDesignView/DesignView/types'
import { usePluginDesignStore } from '../../../stores'

const props = defineProps({
  compt: {
    type: Object as PropType<FieldDataType>,
    required: true
  },
  parentFields: {
    type: Array,
    default: () => []
  }
})

const pluginDesignStore = usePluginDesignStore()

// 输入框的值
const inputValue = ref('')

// 获取占位符文本
const getPlaceholder = () => {
  switch (props.compt.compt) {
    case 'editinput':
      return '请输入'
    case 'numberinput':
      return '请输入数字'
    case 'selectbox':
      return '请选择'
    case 'selectmultibox':
      return '请选择多项'
    case 'dateinput':
      return '请选择日期'
    case 'fieldselect':
      return '请选择字段'
    default:
      return '请输入'
  }
}

// 复制字段
const handleCopy = () => {
  // 创建字段的副本
  const newField = {
    ...props.compt,
    id: pluginDesignStore.getFieldId(), // 生成新的ID
    alias: `${props.compt.alias}_copy` // 修改别名避免重复
  }

  // 添加到主字段列表
  pluginDesignStore.setMainFields(newField)
}

// 删除字段
const handleDelete = () => {
  // 从主字段列表中删除当前字段
  pluginDesignStore.removeMainField(props.compt.id)
}
</script>

<style lang="scss" scoped>
.field-component {
  position: relative;
  padding: 12px;
  border: 1px solid #eaecf2;
  border-radius: 4px;
  background: #fff;

  &:hover {
    .field-actions {
      opacity: 1;
    }
  }
}

.field-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.field-input {
  .input-field {
    width: 100%;
    height: 32px;
    padding: 0 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: #fff;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &::placeholder {
      color: #999;
    }
  }
}

.field-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.action-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }
}

.copy-btn:hover {
  background-color: #e6f7ff;
}

.delete-btn:hover {
  background-color: #fff2f0;
}
</style>

<style lang="scss" scoped>
.field-component {
  padding: 8px;
  font-size: 14px;
  color: #333;
}
</style>
