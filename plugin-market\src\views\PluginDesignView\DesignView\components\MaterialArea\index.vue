<template>
  <div class="material-area" :class="{ collapse: isCollapse, hide: isHide }">
    <div class="material-area-header">
      <div v-show="!isCollapse" class="header-title">组件库</div>
      <div class="header-actions">
        <i class="icon-expand" title="折叠" @click="handleCollapse">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M15 3.5C15 3.08579 14.6642 2.75 14.25 2.75H1.75C1.33579 2.75 1 3.08579 1 3.5C1 3.91421 1.33579 4.25 1.75 4.25H14.25C14.6642 4.25 15 3.91421 15 3.5ZM8.5 8C8.5 7.58579 8.16421 7.25 7.75 7.25H1.75C1.33579 7.25 1 7.58579 1 8C1 8.41421 1.33579 8.75 1.75 8.75H7.75C8.16421 8.75 8.5 8.41421 8.5 8ZM15 7.87261V12.6274C15 13.1583 14.3798 13.4447 13.9784 13.0992L11.2161 10.7218C10.928 10.4738 10.928 10.0262 11.2161 9.77819L13.9784 7.4008C14.3798 7.05529 15 7.34171 15 7.87261Z"
              fill="#3D4766"
            />
            <path
              d="M8.5 8C8.5 7.58579 8.16421 7.25 7.75 7.25H1.75C1.33579 7.25 1 7.58579 1 8C1 8.41421 1.33579 8.75 1.75 8.75H7.75C8.16421 8.75 8.5 8.41421 8.5 8Z"
              fill="#3D4766"
            />
            <path
              d="M8.5 12.5C8.5 12.0858 8.16421 11.75 7.75 11.75H1.75C1.33579 11.75 1 12.0858 1 12.5C1 12.9142 1.33579 13.25 1.75 13.25H7.75C8.16421 13.25 8.5 12.9142 8.5 12.5Z"
              fill="#3D4766"
            />
          </svg>
        </i>
      </div>
    </div>
    <div v-for="(group, index) in materialFieldsData" :key="group.type" class="material-area-content-group">
      <div v-show="!isCollapse" class="group-title">{{ group.title }}</div>
      <div v-show="index !== 0 && isCollapse" class="halving-line"></div>
      <draggable tag="ul" :sort="false" :group="{ name: 'shared', pull: 'clone', put: 'false' }" @end="freshDragOrigin">
        <li
          v-for="field in group.list"
          :key="field.compt"
          class="group-item fw-cell-item"
          @dragstart="flagDraggingFieldType(field)"
          @click.stop="addFormFields(field)"
        >
          <i :class="field.class"></i>
          <div v-show="!isCollapse" class="field-title">{{ field.title }}</div>
        </li>
      </draggable>
    </div>
    <!-- 隐藏后的小按钮，用于重新显示 -->
    <div v-if="isHide" class="material-area-show-btn" @click="handleShow">
      <i class="icon-set"></i>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { VueDraggableNext as draggable } from 'vue-draggable-next'
import { materialFieldsData } from '../../data'
import type { FieldType } from '../../types'
import { usePluginDesignStore } from '@/stores'
import useAddField from '../../hooks/useAddField'

const pluginDesignStore = usePluginDesignStore()
const isCollapse = ref(false)
const isHide = ref(false)

// 折叠面板
const handleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 显示面板
const handleShow = () => {
  isHide.value = false
}

/**
 * @Description: 刷新拖拽元素(重新绑定事件)
 * @param {*} ev
 */
function freshDragOrigin(ev: any) {
  if (ev.from === ev.to) {
    return
  }
  ev.from.insertBefore(ev.item, ev.clone)
  ev.from.removeChild(ev.clone)
}

/**
 * @Description: 记录拖拽组件类型
 * @param {*} field 组件类型
 */
function flagDraggingFieldType(field: FieldType) {
  pluginDesignStore.setDraggingFieldType(field.compt)
}

/**
 * @Description: 添加组件
 * @param {*} field 组件类型
 */
function addFormFields(field: FieldType) {
  useAddField(field.compt)
}
</script>

<style lang="scss">
.material-area {
  position: relative;
  transition: all 0.3s ease;

  .material-area-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      gap: 8px;

      i {
        cursor: pointer;
        font-size: 16px;
        color: #525766;

        &:hover {
          color: #0057ff;
        }
      }
    }
  }

  &.hide {
    width: 30px !important;
    overflow: hidden;
    padding: 0;

    .material-area-header,
    .material-area-content-group {
      display: none;
    }
  }

  .material-area-show-btn {
    position: absolute;
    top: 10px;
    left: 5px;
    width: 20px;
    height: 20px;
    background-color: #0057ff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    i {
      color: white;
      font-size: 12px;
    }
  }
}
</style>
