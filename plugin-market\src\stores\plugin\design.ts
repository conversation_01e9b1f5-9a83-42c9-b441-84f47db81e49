import { defineStore } from 'pinia'
import { type ComponentType, type FieldDataType } from '@/views/PluginDesignView/DesignView/types'

interface PluginDesignState {
  // 主区域字段
  mainFields: FieldDataType[]
  // 拖拽字段信息
  draggingFieldInfo: {
    type: ComponentType | null
  }
  // 字段ID
  fieldId: number
  // 当前选中的字段
  selectedField: FieldDataType | null
}

export const usePluginDesignStore = defineStore('pluginDesign', {
  state: (): PluginDesignState => ({
    mainFields: [],
    draggingFieldInfo: {
      type: null
    },
    fieldId: 0,
    selectedField: null
  }),
  actions: {
    // 设置主区域字段
    setMainFields(field: FieldDataType) {
      this.mainFields.push(field)
    },
    // 删除主区域字段
    removeMainField(fieldId: number) {
      const index = this.mainFields.findIndex(field => field.id === fieldId)
      if (index > -1) {
        this.mainFields.splice(index, 1)
        // 如果删除的是当前选中的字段，清空选中状态
        if (this.selectedField && this.selectedField.id === fieldId) {
          this.selectedField = null
        }
      }
    },
    // 设置拖拽字段类型
    setDraggingFieldType(type: ComponentType) {
      this.draggingFieldInfo.type = type
    },
    // 获取字段ID
    getFieldId() {
      return ++this.fieldId
    },
    // 设置当前选中的字段
    setSelectedField(field: FieldDataType | null) {
      this.selectedField = field
    }
  }
})
