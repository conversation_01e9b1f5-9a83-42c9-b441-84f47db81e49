<template>
  <div class="create-content">
    <div class="plugin-design">
      <!-- 物料区 -->
      <material-area></material-area>
      <!-- 渲染区 -->
      <rendering-area></rendering-area>
      <!-- 配置区 -->
      <configuration-area></configuration-area>
    </div>
  </div>
</template>
<script lang="ts" setup>
import MaterialArea from './components/MaterialArea/index.vue'
import RenderingArea from './components/RenderingArea/index.vue'
import ConfigurationArea from './components/ConfigurationArea/index.vue'
</script>

<style lang="scss">
.create-content {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
}
</style>
