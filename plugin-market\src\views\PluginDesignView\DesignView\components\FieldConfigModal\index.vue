<template>
  <div v-if="visible" class="field-config-modal-overlay" @click="handleOverlayClick">
    <div class="field-config-modal" @click.stop>
      <!-- 弹窗头部 -->
      <div class="modal-header">
        <h3 class="modal-title">配置字段</h3>
        <button class="close-btn" @click="closeModal">
          <i class="icon-close">×</i>
        </button>
      </div>

      <!-- 弹窗内容 -->
      <div class="modal-content">
        <!-- 表格头部 -->
        <div class="table-header">
          <div class="header-cell required">字段名称</div>
          <div class="header-cell">字段提示语</div>
          <div class="header-cell">匹配字段类型</div>
        </div>

        <!-- 字段配置列表 -->
        <div class="field-list">
          <div v-for="(field, index) in fieldConfigs" :key="index" class="field-row">
            <div class="field-cell">
              <input v-model="field.name" type="text" class="field-input" :placeholder="index === 0 ? '请输入' : '单行输入'" />
            </div>
            <div class="field-cell">
              <input v-model="field.placeholder" type="text" class="field-input" :placeholder="index === 0 ? '请输入人学号' : '请输入人学号'" />
            </div>
            <div class="field-cell">
              <select v-model="field.type" class="field-select">
                <option value="type1">字段类型1, 字段类型2</option>
                <option value="type3">字段类型3</option>
                <option value="type4">字段类型4</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 添加字段按钮 -->
        <div class="add-field-section">
          <button class="add-field-btn" @click="addField">
            <i class="icon-plus">+</i>
            添加字段
          </button>
        </div>
      </div>

      <!-- 弹窗底部 -->
      <div class="modal-footer">
        <button class="confirm-btn" @click="confirmConfig">确定</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

interface FieldConfig {
  name: string
  placeholder: string
  type: string
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  modelValue: {
    type: Array as () => FieldConfig[],
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'update:modelValue', 'confirm'])

// 字段配置数据
const fieldConfigs = ref<FieldConfig[]>([
  { name: '请输入', placeholder: '请输入人学号', type: 'type1' },
  { name: '单行输入', placeholder: '请输入人学号', type: 'type3' },
  { name: '下拉框', placeholder: '请输入', type: 'type4' }
])

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      fieldConfigs.value = [...newValue]
    }
  },
  { immediate: true }
)

// 添加字段
const addField = () => {
  fieldConfigs.value.push({
    name: '',
    placeholder: '',
    type: 'type1'
  })
}

// 关闭弹窗
const closeModal = () => {
  emit('update:visible', false)
}

// 点击遮罩层关闭
const handleOverlayClick = () => {
  closeModal()
}

// 确认配置
const confirmConfig = () => {
  emit('update:modelValue', fieldConfigs.value)
  emit('confirm', fieldConfigs.value)
  closeModal()
}
</script>

<style lang="scss" scoped>
.field-config-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.field-config-modal {
  width: 800px;
  max-height: 600px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  .icon-close {
    font-size: 18px;
    color: #999;
  }
}

.modal-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.header-cell {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  padding: 8px 0;

  &.required::after {
    content: '*';
    color: #ff4d4f;
    margin-left: 4px;
  }
}

.field-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.field-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;
  align-items: center;
}

.field-cell {
  display: flex;
  align-items: center;
}

.field-input,
.field-select {
  width: 100%;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background: #fff;
  outline: none;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &::placeholder {
    color: #999;
  }
}

.field-select {
  cursor: pointer;
}

.add-field-section {
  margin-top: 16px;
}

.add-field-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background: #fff;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }

  .icon-plus {
    font-size: 16px;
  }
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
}

.confirm-btn {
  padding: 8px 24px;
  border: none;
  border-radius: 4px;
  background: #1890ff;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #40a9ff;
  }
}
</style>
