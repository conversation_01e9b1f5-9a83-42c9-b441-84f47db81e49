<template>
  <div class="configuration-area">
    <div v-if="selectedField" class="config-panel">
      <!-- 单行输入配置面板 -->
      <div v-if="selectedField.compt === 'editinput'" class="edit-input-config">
        <h3 class="panel-title">字段属性</h3>

        <!-- 字段标题 -->
        <div class="config-item">
          <label class="config-label">字段标题</label>
          <input v-model="fieldTitle" type="text" class="config-input" />
        </div>

        <!-- 字段别名 -->
        <div class="config-item">
          <label class="config-label">字段别名</label>
          <input v-model="fieldAlias" type="text" class="config-input" />
        </div>

        <!-- 标题描述 -->
        <div class="config-item">
          <label class="config-label">标题描述</label>
          <textarea v-model="fieldDescription" class="config-textarea" placeholder="请输入描述"></textarea>
        </div>

        <!-- 格式 -->
        <div class="config-item">
          <label class="config-label">格式</label>
          <select v-model="fieldFormat" class="config-select">
            <option value="email">邮箱</option>
            <option value="phone">手机号</option>
            <option value="text">文本</option>
            <option value="url">网址</option>
          </select>
        </div>

        <!-- 默认值 -->
        <div class="config-item">
          <label class="config-label">默认值</label>
          <input v-model="fieldDefaultValue" type="text" class="config-input" />
        </div>

        <!-- 校验 -->
        <div class="config-item">
          <label class="config-label">校验</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="fieldRequired" type="checkbox" />
              <span>必填</span>
            </label>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="config-item">
          <label class="config-label">操作权限</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="fieldVisible" type="checkbox" />
              <span>可见</span>
            </label>
            <label class="checkbox-item">
              <input v-model="fieldEditable" type="checkbox" />
              <span>可编辑</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 数字输入配置面板 -->
      <div v-else-if="selectedField.compt === 'numberinput'" class="number-input-config">
        <h3 class="panel-title">字段属性</h3>

        <!-- 字段标题 -->
        <div class="config-item">
          <label class="config-label">字段标题</label>
          <input v-model="numberFieldTitle" type="text" class="config-input" />
        </div>

        <!-- 字段别名 -->
        <div class="config-item">
          <label class="config-label">字段别名</label>
          <input v-model="numberFieldAlias" type="text" class="config-input" />
        </div>

        <!-- 标题描述 -->
        <div class="config-item">
          <label class="config-label">标题描述</label>
          <textarea v-model="numberFieldDescription" class="config-textarea" placeholder="请输入描述"></textarea>
        </div>

        <!-- 默认值 -->
        <div class="config-item">
          <label class="config-label">默认值</label>
          <input v-model="numberFieldDefaultValue" type="number" class="config-input" />
        </div>

        <!-- 校验 -->
        <div class="config-item">
          <label class="config-label">校验</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="numberFieldRequired" type="checkbox" />
              <span>必填</span>
            </label>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="config-item">
          <label class="config-label">操作权限</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="numberFieldVisible" type="checkbox" />
              <span>可见</span>
            </label>
            <label class="checkbox-item">
              <input v-model="numberFieldEditable" type="checkbox" />
              <span>可编辑</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 下拉框配置面板 -->
      <div v-else-if="selectedField.compt === 'selectbox'" class="select-box-config">
        <h3 class="panel-title">字段属性</h3>

        <!-- 字段标题 -->
        <div class="config-item">
          <label class="config-label">字段标题</label>
          <input v-model="selectFieldTitle" type="text" class="config-input" />
        </div>

        <!-- 字段别名 -->
        <div class="config-item">
          <label class="config-label">字段别名</label>
          <input v-model="selectFieldAlias" type="text" class="config-input" />
        </div>

        <!-- 标题描述 -->
        <div class="config-item">
          <label class="config-label">标题描述</label>
          <textarea v-model="selectFieldDescription" class="config-textarea" placeholder="请输入描述"></textarea>
        </div>

        <!-- 选项配置 -->
        <div class="config-item">
          <label class="config-label">选项</label>
          <div class="options-list">
            <div v-for="(option, index) in selectOptions" :key="index" class="option-item" :class="{ active: selectedOptionIndex === index }">
              <div class="option-radio">
                <input :id="`option-${index}`" v-model="selectedOptionIndex" :value="index" type="radio" name="option-select" class="radio-input" />
                <label :for="`option-${index}`" class="radio-label">
                  <span class="radio-circle"></span>
                </label>
              </div>
              <div class="option-content">
                <span class="option-text" @click="selectedOptionIndex = index">{{ option.label }}</span>
              </div>
              <div class="option-actions">
                <button class="action-icon-btn drag-btn" title="拖拽排序">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <circle cx="4" cy="4" r="1" fill="#ccc" />
                    <circle cx="12" cy="4" r="1" fill="#ccc" />
                    <circle cx="4" cy="8" r="1" fill="#ccc" />
                    <circle cx="12" cy="8" r="1" fill="#ccc" />
                    <circle cx="4" cy="12" r="1" fill="#ccc" />
                    <circle cx="12" cy="12" r="1" fill="#ccc" />
                  </svg>
                </button>
                <button class="action-icon-btn delete-btn" title="删除" @click="deleteOption(index)">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <rect x="3" y="3" width="10" height="10" rx="1" stroke="#ccc" stroke-width="1" fill="none" />
                    <path d="M6 6L10 10M10 6L6 10" stroke="#ccc" stroke-width="1" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <button class="add-option-btn" @click="addOption">+ 添加选项</button>
        </div>

        <!-- 校验 -->
        <div class="config-item">
          <label class="config-label">校验</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="selectFieldRequired" type="checkbox" />
              <span>必填</span>
            </label>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="config-item">
          <label class="config-label">操作权限</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="selectFieldVisible" type="checkbox" />
              <span>可见</span>
            </label>
            <label class="checkbox-item">
              <input v-model="selectFieldEditable" type="checkbox" />
              <span>可编辑</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 多选框配置面板 -->
      <div v-else-if="selectedField.compt === 'selectmultibox'" class="multi-select-box-config">
        <h3 class="panel-title">字段属性</h3>

        <!-- 字段标题 -->
        <div class="config-item">
          <label class="config-label">字段标题</label>
          <input v-model="multiSelectFieldTitle" type="text" class="config-input" />
        </div>

        <!-- 字段别名 -->
        <div class="config-item">
          <label class="config-label">字段别名</label>
          <input v-model="multiSelectFieldAlias" type="text" class="config-input" />
        </div>

        <!-- 标题描述 -->
        <div class="config-item">
          <label class="config-label">标题描述</label>
          <textarea v-model="multiSelectFieldDescription" class="config-textarea" placeholder="请输入描述"></textarea>
        </div>

        <!-- 选项配置 -->
        <div class="config-item">
          <label class="config-label">选项</label>
          <div class="options-list">
            <div
              v-for="(option, index) in multiSelectOptions"
              :key="index"
              class="option-item"
              :class="{ active: selectedMultiOptionIndex.includes(index) }"
            >
              <div class="option-checkbox">
                <input :id="`multi-option-${index}`" v-model="selectedMultiOptionIndex" :value="index" type="checkbox" class="checkbox-input" />
                <label :for="`multi-option-${index}`" class="checkbox-label">
                  <span class="checkbox-square"></span>
                </label>
              </div>
              <div class="option-content">
                <span class="option-text" @click="toggleMultiOption(index)">{{ option.label }}</span>
              </div>
              <div class="option-actions">
                <button class="action-icon-btn drag-btn" title="拖拽排序">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <circle cx="4" cy="4" r="1" fill="#ccc" />
                    <circle cx="12" cy="4" r="1" fill="#ccc" />
                    <circle cx="4" cy="8" r="1" fill="#ccc" />
                    <circle cx="12" cy="8" r="1" fill="#ccc" />
                    <circle cx="4" cy="12" r="1" fill="#ccc" />
                    <circle cx="12" cy="12" r="1" fill="#ccc" />
                  </svg>
                </button>
                <button class="action-icon-btn delete-btn" title="删除" @click="deleteMultiOption(index)">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <rect x="3" y="3" width="10" height="10" rx="1" stroke="#ccc" stroke-width="1" fill="none" />
                    <path d="M6 6L10 10M10 6L6 10" stroke="#ccc" stroke-width="1" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          <button class="add-option-btn" @click="addMultiOption">+ 添加选项</button>
        </div>

        <!-- 校验 -->
        <div class="config-item">
          <label class="config-label">校验</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="multiSelectFieldRequired" type="checkbox" />
              <span>必填</span>
            </label>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="config-item">
          <label class="config-label">操作权限</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="multiSelectFieldVisible" type="checkbox" />
              <span>可见</span>
            </label>
            <label class="checkbox-item">
              <input v-model="multiSelectFieldEditable" type="checkbox" />
              <span>可编辑</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 日期输入配置面板 -->
      <div v-else-if="selectedField.compt === 'dateinput'" class="date-input-config">
        <h3 class="panel-title">字段属性</h3>

        <!-- 字段标题 -->
        <div class="config-item">
          <label class="config-label">字段标题</label>
          <input v-model="dateFieldTitle" type="text" class="config-input" />
        </div>

        <!-- 字段别名 -->
        <div class="config-item">
          <label class="config-label">字段别名</label>
          <input v-model="dateFieldAlias" type="text" class="config-input" />
        </div>

        <!-- 标题描述 -->
        <div class="config-item">
          <label class="config-label">标题描述</label>
          <textarea v-model="dateFieldDescription" class="config-textarea" placeholder="请输入描述"></textarea>
        </div>

        <!-- 格式 -->
        <div class="config-item">
          <label class="config-label">格式</label>
          <select v-model="dateFieldFormat" class="config-select">
            <option value="datetime">年-月-日 时:分</option>
            <option value="date">年-月-日</option>
            <option value="time">时:分</option>
          </select>
        </div>

        <!-- 默认值 -->
        <div class="config-item">
          <label class="config-label">默认值</label>
          <input v-model="dateFieldDefaultValue" type="datetime-local" class="config-input" />
        </div>

        <!-- 校验 -->
        <div class="config-item">
          <label class="config-label">校验</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="dateFieldRequired" type="checkbox" />
              <span>必填</span>
            </label>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="config-item">
          <label class="config-label">操作权限</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="dateFieldVisible" type="checkbox" />
              <span>可见</span>
            </label>
            <label class="checkbox-item">
              <input v-model="dateFieldEditable" type="checkbox" />
              <span>可编辑</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 字段选择配置面板 -->
      <div v-else-if="selectedField.compt === 'fieldselect'" class="field-select-config">
        <h3 class="panel-title">字段属性</h3>

        <!-- 字段标题 -->
        <div class="config-item">
          <label class="config-label">字段标题</label>
          <input v-model="fieldSelectTitle" type="text" class="config-input" />
        </div>

        <!-- 字段别名 -->
        <div class="config-item">
          <label class="config-label">字段别名</label>
          <input v-model="fieldSelectAlias" type="text" class="config-input" />
        </div>

        <!-- 标题描述 -->
        <div class="config-item">
          <label class="config-label">标题描述</label>
          <textarea v-model="fieldSelectDescription" class="config-textarea" placeholder="请输入描述"></textarea>
        </div>

        <!-- 配置字段 -->
        <div class="config-item">
          <label class="config-label">配置字段</label>
          <button class="config-field-btn" @click="openFieldConfig">
            <i class="icon-settings"></i>
            配置字段
          </button>
        </div>

        <!-- 校验 -->
        <div class="config-item">
          <label class="config-label">校验</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="fieldSelectRequired" type="checkbox" />
              <span>必填</span>
            </label>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="config-item">
          <label class="config-label">操作权限</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="fieldSelectVisible" type="checkbox" />
              <span>可见</span>
            </label>
            <label class="checkbox-item">
              <input v-model="fieldSelectEditable" type="checkbox" />
              <span>可编辑</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 其他字段类型的配置面板可以在这里添加 -->
      <div v-else class="default-config">
        <h3 class="panel-title">字段属性</h3>
        <p class="no-config">该字段类型的配置面板正在开发中...</p>
      </div>
    </div>

    <!-- 未选中字段时的提示 -->
    <div v-else class="no-selection">
      <p>请选择一个字段来配置其属性</p>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { usePluginDesignStore } from '../../../../../stores'

const pluginDesignStore = usePluginDesignStore()

// 获取当前选中的字段
const selectedField = computed(() => pluginDesignStore.selectedField)

// 单行输入字段配置数据
const fieldTitle = ref('单行输入')
const fieldAlias = ref('6')
const fieldDescription = ref('')
const fieldFormat = ref('email')
const fieldDefaultValue = ref('<EMAIL>')
const fieldRequired = ref(false)
const fieldVisible = ref(true)
const fieldEditable = ref(true)

// 数字输入字段配置数据
const numberFieldTitle = ref('数字输入')
const numberFieldAlias = ref('6')
const numberFieldDescription = ref('')
const numberFieldDefaultValue = ref('100')
const numberFieldRequired = ref(false)
const numberFieldVisible = ref(true)
const numberFieldEditable = ref(true)

// 下拉框字段配置数据
const selectFieldTitle = ref('下拉框')
const selectFieldAlias = ref('6')
const selectFieldDescription = ref('')
const selectFieldRequired = ref(false)
const selectFieldVisible = ref(true)
const selectFieldEditable = ref(true)

// 下拉框选项数据
const selectOptions = ref([
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3' }
])

// 当前选中的选项索引
const selectedOptionIndex = ref(0)

// 多选框字段配置数据
const multiSelectFieldTitle = ref('下拉复选框')
const multiSelectFieldAlias = ref('6')
const multiSelectFieldDescription = ref('')
const multiSelectFieldRequired = ref(false)
const multiSelectFieldVisible = ref(true)
const multiSelectFieldEditable = ref(true)

// 多选框选项数据
const multiSelectOptions = ref([
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3' }
])

// 当前选中的多选选项索引
const selectedMultiOptionIndex = ref<number[]>([0])

// 日期字段配置数据
const dateFieldTitle = ref('日期')
const dateFieldAlias = ref('date')
const dateFieldDescription = ref('')
const dateFieldFormat = ref('datetime')
const dateFieldDefaultValue = ref('2024-12-31T08:00')
const dateFieldRequired = ref(false)
const dateFieldVisible = ref(true)
const dateFieldEditable = ref(true)

// 字段选择配置数据
const fieldSelectTitle = ref('字段选择')
const fieldSelectAlias = ref('6')
const fieldSelectDescription = ref('')
const fieldSelectRequired = ref(false)
const fieldSelectVisible = ref(true)
const fieldSelectEditable = ref(true)

// 更新字段配置到store
const updateFieldConfig = () => {
  if (selectedField.value && selectedField.value.compt === 'selectbox') {
    pluginDesignStore.setFieldConfig(selectedField.value.id, {
      options: selectOptions.value
    })
  }
}

// 添加选项
const addOption = () => {
  const newIndex = selectOptions.value.length + 1
  selectOptions.value.push({
    value: `option${newIndex}`,
    label: `选项${newIndex}`
  })
  updateFieldConfig()
}

// 复制选项
const copyOption = (index: number) => {
  const originalOption = selectOptions.value[index]
  const newOption = {
    value: `${originalOption.value}_copy`,
    label: `${originalOption.label}_副本`
  }
  selectOptions.value.splice(index + 1, 0, newOption)
  updateFieldConfig()
}

// 删除选项
const deleteOption = (index: number) => {
  if (selectOptions.value.length > 1) {
    selectOptions.value.splice(index, 1)
    // 调整选中索引
    if (selectedOptionIndex.value >= selectOptions.value.length) {
      selectedOptionIndex.value = selectOptions.value.length - 1
    }
    updateFieldConfig()
  }
}

// 编辑选项
const editOption = (index: number) => {
  selectedOptionIndex.value = index
  // 这里可以添加更多编辑逻辑，比如弹出编辑框等
}

// 更新多选字段配置到store
const updateMultiFieldConfig = () => {
  if (selectedField.value && selectedField.value.compt === 'selectmultibox') {
    pluginDesignStore.setFieldConfig(selectedField.value.id, {
      options: multiSelectOptions.value
    })
  }
}

// 添加多选选项
const addMultiOption = () => {
  const newIndex = multiSelectOptions.value.length + 1
  multiSelectOptions.value.push({
    value: `option${newIndex}`,
    label: `选项${newIndex}`
  })
  updateMultiFieldConfig()
}

// 删除多选选项
const deleteMultiOption = (index: number) => {
  if (multiSelectOptions.value.length > 1) {
    multiSelectOptions.value.splice(index, 1)
    // 调整选中索引
    const newSelectedIndex = selectedMultiOptionIndex.value.filter((i: number) => i !== index).map((i: number) => (i > index ? i - 1 : i))
    selectedMultiOptionIndex.value = newSelectedIndex
    updateMultiFieldConfig()
  }
}

// 切换多选选项
const toggleMultiOption = (index: number) => {
  const currentIndex = selectedMultiOptionIndex.value.indexOf(index)
  if (currentIndex > -1) {
    selectedMultiOptionIndex.value.splice(currentIndex, 1)
  } else {
    selectedMultiOptionIndex.value.push(index)
  }
}

// 监听选中字段变化，更新配置数据
watch(
  selectedField,
  (newField) => {
    if (newField && newField.compt === 'editinput') {
      // 根据字段数据初始化单行输入配置
      fieldTitle.value = '单行输入'
      fieldAlias.value = newField.alias || '6'
      fieldDescription.value = ''
      fieldFormat.value = 'email'
      fieldDefaultValue.value = '<EMAIL>'
      fieldRequired.value = false
      fieldVisible.value = true
      fieldEditable.value = true
    } else if (newField && newField.compt === 'numberinput') {
      // 根据字段数据初始化数字输入配置
      numberFieldTitle.value = '数字输入'
      numberFieldAlias.value = newField.alias || '6'
      numberFieldDescription.value = ''
      numberFieldDefaultValue.value = '100'
      numberFieldRequired.value = false
      numberFieldVisible.value = true
      numberFieldEditable.value = true
    } else if (newField && newField.compt === 'selectbox') {
      // 根据字段数据初始化下拉框配置
      selectFieldTitle.value = '下拉框'
      selectFieldAlias.value = newField.alias || '6'
      selectFieldDescription.value = ''
      selectFieldRequired.value = false
      selectFieldVisible.value = true
      selectFieldEditable.value = true

      // 从store获取已有配置或设置默认值
      const existingConfig = pluginDesignStore.getFieldConfig(newField.id)
      if (existingConfig.options) {
        selectOptions.value = existingConfig.options
      } else {
        // 重置选项为默认值并保存到store
        selectOptions.value = [
          { value: 'option1', label: '选项1' },
          { value: 'option2', label: '选项2' },
          { value: 'option3', label: '选项3' }
        ]
        updateFieldConfig()
      }
    } else if (newField && newField.compt === 'selectmultibox') {
      // 根据字段数据初始化多选框配置
      multiSelectFieldTitle.value = '下拉复选框'
      multiSelectFieldAlias.value = newField.alias || '6'
      multiSelectFieldDescription.value = ''
      multiSelectFieldRequired.value = false
      multiSelectFieldVisible.value = true
      multiSelectFieldEditable.value = true

      // 从store获取已有配置或设置默认值
      const existingConfig = pluginDesignStore.getFieldConfig(newField.id)
      if (existingConfig.options) {
        multiSelectOptions.value = existingConfig.options
      } else {
        // 重置选项为默认值并保存到store
        multiSelectOptions.value = [
          { value: 'option1', label: '选项1' },
          { value: 'option2', label: '选项2' },
          { value: 'option3', label: '选项3' }
        ]
        updateMultiFieldConfig()
      }
      selectedMultiOptionIndex.value = [0]
    } else if (newField && newField.compt === 'dateinput') {
      // 根据字段数据初始化日期字段配置
      dateFieldTitle.value = '日期'
      dateFieldAlias.value = newField.alias || 'date'
      dateFieldDescription.value = ''
      dateFieldFormat.value = 'datetime'
      dateFieldDefaultValue.value = '2024-12-31T08:00'
      dateFieldRequired.value = false
      dateFieldVisible.value = true
      dateFieldEditable.value = true
    } else if (newField && newField.compt === 'fieldselect') {
      // 根据字段数据初始化字段选择配置
      fieldSelectTitle.value = '字段选择'
      fieldSelectAlias.value = newField.alias || '6'
      fieldSelectDescription.value = ''
      fieldSelectRequired.value = false
      fieldSelectVisible.value = true
      fieldSelectEditable.value = true
    }
  },
  { immediate: true }
)

// 打开字段配置
const openFieldConfig = () => {
  // 这里可以打开字段配置弹窗或跳转到配置页面
  console.log('打开字段配置')
}

// 监听选项变化，同步到store
watch(
  selectOptions,
  () => {
    updateFieldConfig()
  },
  { deep: true }
)
</script>

<style lang="scss">
.configuration-area {
  width: 300px;
  height: 100vh;
  background: #fff;
  border-left: 1px solid #eaecf2;
  overflow-y: auto;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
}

.config-panel {
  padding: 20px;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
}

.config-item {
  margin-bottom: 16px;
}

.config-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.config-input {
  width: 100%;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background: #fff;
  outline: none;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &::placeholder {
    color: #999;
  }
}

.config-textarea {
  width: 100%;
  min-height: 60px;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background: #fff;
  outline: none;
  resize: vertical;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &::placeholder {
    color: #999;
  }
}

.config-select {
  width: 100%;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background: #fff;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  cursor: pointer;

  input[type='checkbox'] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  span {
    font-size: 14px;
    color: #333;
    user-select: none;
  }
}

.no-selection {
  padding: 40px 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.config-field-btn {
  width: 100%;
  height: 32px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }

  .icon-settings {
    font-size: 16px;
  }
}

.default-config {
  padding: 20px;
}

.no-config {
  color: #999;
  font-size: 14px;
  text-align: center;
  margin-top: 20px;
}

// 下拉框配置样式
.options-list {
  margin-bottom: 12px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
  padding: 8px 12px;
  background: #fff;
  transition: all 0.2s ease;
  border-radius: 4px;

  &:hover {
    background: #f8f9fa;
  }

  &.active {
    background: #f0f8ff;
  }
}

.option-radio {
  display: flex;
  align-items: center;
}

.radio-input {
  display: none;
}

.radio-label {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.radio-circle {
  width: 14px;
  height: 14px;
  border: 1px solid #d9d9d9;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;

  &::after {
    content: '';
    width: 6px;
    height: 6px;
    background: #1890ff;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform 0.2s ease;
  }
}

.radio-input:checked + .radio-label .radio-circle {
  border-color: #1890ff;

  &::after {
    transform: translate(-50%, -50%) scale(1);
  }
}

// 多选框样式
.option-checkbox {
  display: flex;
  align-items: center;
}

.checkbox-square {
  width: 14px;
  height: 14px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  position: relative;
  transition: all 0.2s ease;

  &::after {
    content: '';
    width: 6px;
    height: 3px;
    border: 2px solid #1890ff;
    border-top: none;
    border-right: none;
    position: absolute;
    top: 2px;
    left: 2px;
    transform: rotate(-45deg) scale(0);
    transition: transform 0.2s ease;
  }
}

.checkbox-input:checked + .checkbox-label .checkbox-square {
  border-color: #1890ff;
  background: #1890ff;

  &::after {
    border-color: #fff;
    transform: rotate(-45deg) scale(1);
  }
}

.option-content {
  flex: 1;
}

.option-text {
  font-size: 14px;
  color: #333;
  cursor: pointer;
  padding: 4px 0;
  display: block;
}

.option-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.option-item:hover .option-actions {
  opacity: 1;
}

.action-icon-btn {
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }

  svg {
    transition: all 0.2s ease;
  }
}

.drag-btn:hover svg circle {
  fill: #666;
}

.delete-btn:hover {
  background-color: #fff2f0;

  svg {
    rect,
    path {
      stroke: #ff4d4f;
    }
  }
}

.add-option-btn {
  width: 100%;
  height: 32px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background: transparent;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }
}
</style>
