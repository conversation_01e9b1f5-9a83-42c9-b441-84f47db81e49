<template>
  <div class="configuration-area">
    <div v-if="selectedField" class="config-panel">
      <!-- 单行输入配置面板 -->
      <div v-if="selectedField.compt === 'editinput'" class="edit-input-config">
        <h3 class="panel-title">字段属性</h3>

        <!-- 字段标题 -->
        <div class="config-item">
          <label class="config-label">字段标题</label>
          <input v-model="fieldTitle" type="text" class="config-input" />
        </div>

        <!-- 字段别名 -->
        <div class="config-item">
          <label class="config-label">字段别名</label>
          <input v-model="fieldAlias" type="text" class="config-input" />
        </div>

        <!-- 标题描述 -->
        <div class="config-item">
          <label class="config-label">标题描述</label>
          <textarea v-model="fieldDescription" class="config-textarea" placeholder="请输入描述"></textarea>
        </div>

        <!-- 格式 -->
        <div class="config-item">
          <label class="config-label">格式</label>
          <select v-model="fieldFormat" class="config-select">
            <option value="email">邮箱</option>
            <option value="phone">手机号</option>
            <option value="text">文本</option>
            <option value="url">网址</option>
          </select>
        </div>

        <!-- 默认值 -->
        <div class="config-item">
          <label class="config-label">默认值</label>
          <input v-model="fieldDefaultValue" type="text" class="config-input" />
        </div>

        <!-- 校验 -->
        <div class="config-item">
          <label class="config-label">校验</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="fieldRequired" type="checkbox" />
              <span>必填</span>
            </label>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="config-item">
          <label class="config-label">操作权限</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="fieldVisible" type="checkbox" />
              <span>可见</span>
            </label>
            <label class="checkbox-item">
              <input v-model="fieldEditable" type="checkbox" />
              <span>可编辑</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 数字输入配置面板 -->
      <div v-else-if="selectedField.compt === 'numberinput'" class="number-input-config">
        <h3 class="panel-title">字段属性</h3>

        <!-- 字段标题 -->
        <div class="config-item">
          <label class="config-label">字段标题</label>
          <input v-model="numberFieldTitle" type="text" class="config-input" />
        </div>

        <!-- 字段别名 -->
        <div class="config-item">
          <label class="config-label">字段别名</label>
          <input v-model="numberFieldAlias" type="text" class="config-input" />
        </div>

        <!-- 标题描述 -->
        <div class="config-item">
          <label class="config-label">标题描述</label>
          <textarea v-model="numberFieldDescription" class="config-textarea" placeholder="请输入描述"></textarea>
        </div>

        <!-- 默认值 -->
        <div class="config-item">
          <label class="config-label">默认值</label>
          <input v-model="numberFieldDefaultValue" type="number" class="config-input" />
        </div>

        <!-- 校验 -->
        <div class="config-item">
          <label class="config-label">校验</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="numberFieldRequired" type="checkbox" />
              <span>必填</span>
            </label>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="config-item">
          <label class="config-label">操作权限</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="numberFieldVisible" type="checkbox" />
              <span>可见</span>
            </label>
            <label class="checkbox-item">
              <input v-model="numberFieldEditable" type="checkbox" />
              <span>可编辑</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 下拉框配置面板 -->
      <div v-else-if="selectedField.compt === 'selectbox'" class="select-box-config">
        <h3 class="panel-title">字段属性</h3>

        <!-- 字段标题 -->
        <div class="config-item">
          <label class="config-label">字段标题</label>
          <input v-model="selectFieldTitle" type="text" class="config-input" />
        </div>

        <!-- 字段别名 -->
        <div class="config-item">
          <label class="config-label">字段别名</label>
          <input v-model="selectFieldAlias" type="text" class="config-input" />
        </div>

        <!-- 标题描述 -->
        <div class="config-item">
          <label class="config-label">标题描述</label>
          <textarea v-model="selectFieldDescription" class="config-textarea" placeholder="请输入描述"></textarea>
        </div>

        <!-- 选项配置 -->
        <div class="config-item">
          <label class="config-label">选项</label>
          <div class="options-list">
            <div v-for="(option, index) in selectOptions" :key="index" class="option-item">
              <input v-model="option.label" type="text" class="option-input" placeholder="选项名称" />
              <button class="option-btn copy-option-btn" title="复制" @click="copyOption(index)">
                <i class="icon-copy"></i>
              </button>
              <button class="option-btn delete-option-btn" title="删除" @click="deleteOption(index)">
                <i class="icon-delete"></i>
              </button>
            </div>
          </div>
          <button class="add-option-btn" @click="addOption">+ 添加选项</button>
        </div>

        <!-- 校验 -->
        <div class="config-item">
          <label class="config-label">校验</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="selectFieldRequired" type="checkbox" />
              <span>必填</span>
            </label>
          </div>
        </div>

        <!-- 操作权限 -->
        <div class="config-item">
          <label class="config-label">操作权限</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input v-model="selectFieldVisible" type="checkbox" />
              <span>可见</span>
            </label>
            <label class="checkbox-item">
              <input v-model="selectFieldEditable" type="checkbox" />
              <span>可编辑</span>
            </label>
          </div>
        </div>
      </div>

      <!-- 其他字段类型的配置面板可以在这里添加 -->
      <div v-else class="default-config">
        <h3 class="panel-title">字段属性</h3>
        <p class="no-config">该字段类型的配置面板正在开发中...</p>
      </div>
    </div>

    <!-- 未选中字段时的提示 -->
    <div v-else class="no-selection">
      <p>请选择一个字段来配置其属性</p>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { usePluginDesignStore } from '../../../../../stores'

const pluginDesignStore = usePluginDesignStore()

// 获取当前选中的字段
const selectedField = computed(() => pluginDesignStore.selectedField)

// 单行输入字段配置数据
const fieldTitle = ref('单行输入')
const fieldAlias = ref('6')
const fieldDescription = ref('')
const fieldFormat = ref('email')
const fieldDefaultValue = ref('<EMAIL>')
const fieldRequired = ref(false)
const fieldVisible = ref(true)
const fieldEditable = ref(true)

// 数字输入字段配置数据
const numberFieldTitle = ref('数字输入')
const numberFieldAlias = ref('6')
const numberFieldDescription = ref('')
const numberFieldDefaultValue = ref('100')
const numberFieldRequired = ref(false)
const numberFieldVisible = ref(true)
const numberFieldEditable = ref(true)

// 下拉框字段配置数据
const selectFieldTitle = ref('下拉框')
const selectFieldAlias = ref('6')
const selectFieldDescription = ref('')
const selectFieldRequired = ref(false)
const selectFieldVisible = ref(true)
const selectFieldEditable = ref(true)

// 下拉框选项数据
const selectOptions = ref([
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3' }
])

// 添加选项
const addOption = () => {
  const newIndex = selectOptions.value.length + 1
  selectOptions.value.push({
    value: `option${newIndex}`,
    label: `选项${newIndex}`
  })
}

// 复制选项
const copyOption = (index: number) => {
  const originalOption = selectOptions.value[index]
  const newOption = {
    value: `${originalOption.value}_copy`,
    label: `${originalOption.label}_副本`
  }
  selectOptions.value.splice(index + 1, 0, newOption)
}

// 删除选项
const deleteOption = (index: number) => {
  if (selectOptions.value.length > 1) {
    selectOptions.value.splice(index, 1)
  }
}

// 监听选中字段变化，更新配置数据
watch(
  selectedField,
  (newField) => {
    if (newField && newField.compt === 'editinput') {
      // 根据字段数据初始化单行输入配置
      fieldTitle.value = '单行输入'
      fieldAlias.value = newField.alias || '6'
      fieldDescription.value = ''
      fieldFormat.value = 'email'
      fieldDefaultValue.value = '<EMAIL>'
      fieldRequired.value = false
      fieldVisible.value = true
      fieldEditable.value = true
    } else if (newField && newField.compt === 'numberinput') {
      // 根据字段数据初始化数字输入配置
      numberFieldTitle.value = '数字输入'
      numberFieldAlias.value = newField.alias || '6'
      numberFieldDescription.value = ''
      numberFieldDefaultValue.value = '100'
      numberFieldRequired.value = false
      numberFieldVisible.value = true
      numberFieldEditable.value = true
    } else if (newField && newField.compt === 'selectbox') {
      // 根据字段数据初始化下拉框配置
      selectFieldTitle.value = '下拉框'
      selectFieldAlias.value = newField.alias || '6'
      selectFieldDescription.value = ''
      selectFieldRequired.value = false
      selectFieldVisible.value = true
      selectFieldEditable.value = true
      // 重置选项为默认值
      selectOptions.value = [
        { value: 'option1', label: '选项1' },
        { value: 'option2', label: '选项2' },
        { value: 'option3', label: '选项3' }
      ]
    }
  },
  { immediate: true }
)
</script>

<style lang="scss">
.configuration-area {
  width: 300px;
  height: 100vh;
  background: #fff;
  border-left: 1px solid #eaecf2;
  overflow-y: auto;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
}

.config-panel {
  padding: 20px;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 20px 0;
}

.config-item {
  margin-bottom: 16px;
}

.config-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.config-input {
  width: 100%;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background: #fff;
  outline: none;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &::placeholder {
    color: #999;
  }
}

.config-textarea {
  width: 100%;
  min-height: 60px;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background: #fff;
  outline: none;
  resize: vertical;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &::placeholder {
    color: #999;
  }
}

.config-select {
  width: 100%;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background: #fff;
  outline: none;
  cursor: pointer;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  cursor: pointer;

  input[type='checkbox'] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  span {
    font-size: 14px;
    color: #333;
    user-select: none;
  }
}

.no-selection {
  padding: 40px 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.default-config {
  padding: 20px;
}

.no-config {
  color: #999;
  font-size: 14px;
  text-align: center;
  margin-top: 20px;
}

// 下拉框配置样式
.options-list {
  margin-bottom: 12px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px;
  border: 1px solid #eaecf2;
  border-radius: 4px;
  background: #fafafa;
}

.option-input {
  flex: 1;
  height: 28px;
  padding: 0 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  background: #fff;
  outline: none;

  &:focus {
    border-color: #1890ff;
  }
}

.option-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }

  .icon-copy,
  .icon-delete {
    width: 12px;
    height: 12px;
  }
}

.copy-option-btn:hover {
  background-color: #e6f7ff;
}

.delete-option-btn:hover {
  background-color: #fff2f0;
}

.add-option-btn {
  width: 100%;
  height: 32px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background: transparent;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }
}
</style>
