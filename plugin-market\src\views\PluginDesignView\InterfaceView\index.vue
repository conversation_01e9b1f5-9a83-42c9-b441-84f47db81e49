<script setup lang="ts">
import Main from './Main.vue'
import RequestMethod from './RequestMethod.vue'
import ResponseHeaderTable from './ResponseHeaderTable.vue'
</script>

<template>
  <div class="app">
    <Main>
      <template #response-header><ResponseHeaderTable /></template>
      <template #request-method><RequestMethod /></template>
    </Main>
  </div>
</template>

<style>
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fc;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
}
</style>
